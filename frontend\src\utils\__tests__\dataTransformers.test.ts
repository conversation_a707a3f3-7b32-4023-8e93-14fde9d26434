import { DataTransformerFactory } from '../dataTransformers';
import { ProcessData, SourceInfoData, SentimentsData } from '@/types/dashboard';

describe('DataTransformerFactory', () => {
  describe('ProcessTransformer', () => {
    const mockProcessData = {
      twitter: [
        {
          datetime: '2025-07-01T00:00:00.000+03:30',
          timestamp: 1751315400000,
          count: 30,
        },
        {
          datetime: '2025-07-02T00:00:00.000+03:30',
          timestamp: 1751401800000,
          count: 61,
        },
      ],
    };

    it('should transform process data for line chart', () => {
      const result = DataTransformerFactory.transform(
        mockProcessData,
        'process',
        'line'
      );

      expect(result.series).toBeDefined();
      expect(result.series).toHaveLength(1);
      expect(result.series![0].name).toBe('تعداد');
      expect(result.series![0].data).toHaveLength(2);
      expect(result.categories).toHaveLength(2);
    });

    it('should transform process data for bar chart', () => {
      const result = DataTransformerFactory.transform(
        mockProcessData,
        'process',
        'bar'
      );

      expect(result.series).toBeDefined();
      expect(result.series).toHaveLength(1);
      expect(result.series![0].name).toBe('تعداد');
      expect(result.series![0].data).toEqual([30, 61]);
      expect(result.categories).toHaveLength(2);
    });

    it('should transform process data for badge', () => {
      const result = DataTransformerFactory.transform(
        mockProcessData,
        'process',
        'badge'
      );

      expect(result.value).toBe(91); // 30 + 61
    });
  });

  describe('SourceInfoTransformer', () => {
    const mockSourceData: SourceInfoData[] = [
      {
        source: 'Twitter',
        count: 150,
        percentage: 60,
      },
      {
        source: 'Instagram',
        count: 100,
        percentage: 40,
      },
    ];

    it('should transform source data for pie chart', () => {
      const result = DataTransformerFactory.transform(
        mockSourceData,
        'source_info',
        'pie'
      );

      expect(result.series).toBeDefined();
      expect(result.series).toHaveLength(1);
      expect(result.series![0].name).toBe('منابع');
      expect(result.series![0].data).toHaveLength(2);
      expect(result.series![0].data[0]).toEqual({
        name: 'Twitter',
        y: 150,
      });
    });

    it('should transform source data for bar chart', () => {
      const result = DataTransformerFactory.transform(
        mockSourceData,
        'source_info',
        'bar'
      );

      expect(result.series).toBeDefined();
      expect(result.series![0].data).toEqual([150, 100]);
      expect(result.categories).toEqual(['Twitter', 'Instagram']);
    });

    it('should transform source data for table', () => {
      const result = DataTransformerFactory.transform(
        mockSourceData,
        'source_info',
        'table'
      );

      expect(result.data).toBeDefined();
      expect(result.data).toHaveLength(2);
      expect(result.data![0]).toEqual({
        منبع: 'Twitter',
        تعداد: 150,
        درصد: '60%',
      });
    });
  });

  describe('SentimentsTransformer', () => {
    const mockSentimentsData: SentimentsData[] = [
      {
        sentiment: 'positive',
        count: 80,
        percentage: 50,
      },
      {
        sentiment: 'negative',
        count: 50,
        percentage: 31.25,
      },
      {
        sentiment: 'neutral',
        count: 30,
        percentage: 18.75,
      },
    ];

    it('should transform sentiments data for pie chart', () => {
      const result = DataTransformerFactory.transform(
        mockSentimentsData,
        'sentiments',
        'pie'
      );

      expect(result.series).toBeDefined();
      expect(result.series![0].name).toBe('احساسات');
      expect(result.series![0].data).toHaveLength(3);
      expect(result.series![0].data[0]).toEqual({
        name: 'مثبت',
        y: 80,
      });
      expect(result.series![0].data[1]).toEqual({
        name: 'منفی',
        y: 50,
      });
      expect(result.series![0].data[2]).toEqual({
        name: 'خنثی',
        y: 30,
      });
    });

    it('should transform sentiments data for bar chart', () => {
      const result = DataTransformerFactory.transform(
        mockSentimentsData,
        'sentiments',
        'bar'
      );

      expect(result.series![0].data).toEqual([80, 50, 30]);
      expect(result.categories).toEqual(['مثبت', 'منفی', 'خنثی']);
    });
  });

  describe('Error handling', () => {
    it('should handle unknown report type gracefully', () => {
      const result = DataTransformerFactory.transform(
        { some: 'data' },
        'unknown_type',
        'bar'
      );

      expect(result.data).toEqual({ some: 'data' });
    });

    it('should handle transformation errors gracefully', () => {
      const result = DataTransformerFactory.transform(
        null,
        'process',
        'line'
      );

      expect(result.data).toBeNull();
    });
  });

  describe('Multiple source data', () => {
    const mockMultiSourceData = {
      twitter: [
        { datetime: '2025-07-01T00:00:00.000+03:30', timestamp: 1751315400000, count: 30 },
        { datetime: '2025-07-02T00:00:00.000+03:30', timestamp: 1751401800000, count: 61 },
      ],
      instagram: [
        { datetime: '2025-07-01T00:00:00.000+03:30', timestamp: 1751315400000, count: 25 },
        { datetime: '2025-07-02T00:00:00.000+03:30', timestamp: 1751401800000, count: 45 },
      ],
    };

    it('should transform multi-source data correctly', () => {
      const result = DataTransformerFactory.transform(
        mockMultiSourceData,
        'advance',
        'line'
      );

      expect(result.series).toBeDefined();
      expect(result.series).toHaveLength(2);
      expect(result.series![0].name).toBe('twitter');
      expect(result.series![1].name).toBe('instagram');
      expect(result.series![0].data).toEqual([30, 61]);
      expect(result.series![1].data).toEqual([25, 45]);
      expect(result.categories).toHaveLength(2);
    });
  });

  describe('Cloud data transformation', () => {
    const mockCloudData = [
      { text: 'کلمه اول', weight: 100 },
      { text: 'کلمه دوم', weight: 80 },
      { text: 'کلمه سوم', weight: 60 },
    ];

    it('should transform cloud data for word cloud', () => {
      const result = DataTransformerFactory.transform(
        mockCloudData,
        'cloud',
        'cloud'
      );

      expect(result.data).toBeDefined();
      expect(result.data).toHaveLength(3);
      expect(result.data![0]).toEqual({
        name: 'کلمه اول',
        weight: 100,
      });
    });

    it('should transform cloud data for bar chart', () => {
      const result = DataTransformerFactory.transform(
        mockCloudData,
        'cloud',
        'bar'
      );

      expect(result.series![0].data).toEqual([100, 80, 60]);
      expect(result.categories).toEqual(['کلمه اول', 'کلمه دوم', 'کلمه سوم']);
    });
  });
});
